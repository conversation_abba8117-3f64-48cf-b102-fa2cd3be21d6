# 物品管理功能说明

## 📋 功能概述

物品管理模块用于管理幼儿园的各类物品，包括办公用品、教学用品、清洁用品、食品原料等。支持物品的新增、查看、编辑和库存管理。

## 🗄️ 数据库设计

### 物品信息表 (kg_item)
- **item_id**: 物品ID（主键，自增）
- **item_code**: 物品编码（唯一，格式：WP + YYYYMMDD + 4位随机数）
- **item_name**: 物品名称（最大100字符）
- **category_id**: 类别ID（关联kg_item_category表）
- **specification**: 规格型号（可选，最大100字符）
- **unit**: 计量单位（默认"个"，最大20字符）
- **unit_price**: 单价（decimal(10,2)）
- **min_stock**: 最低库存（整数）
- **max_stock**: 最高库存（整数）
- **current_stock**: 当前库存（整数）
- **status**: 状态（0正常 1停用）
- **com_id**: 公司ID（多租户隔离）
- **create_by**: 创建者
- **create_time**: 创建时间
- **update_by**: 更新者
- **update_time**: 更新时间
- **remark**: 备注（最大500字符）

### 物品类别表 (kg_item_category)
- **category_id**: 类别ID（主键，自增）
- **category_name**: 类别名称（最大50字符）
- **category_code**: 类别编码（最大20字符）
- **parent_id**: 父类别ID（支持层级分类）
- **sort_order**: 排序
- **status**: 状态（0正常 1停用）
- **com_id**: 公司ID（多租户隔离）
- **create_by**: 创建者
- **create_time**: 创建时间
- **update_by**: 更新者
- **update_time**: 更新时间
- **remark**: 备注（最大500字符）

## 🎯 功能特性

### 1. 新增物品
- **智能编码生成**: 自动生成格式为 `WP + YYYYMMDD + 4位随机数` 的物品编码
- **分类管理**: 支持选择预设的物品类别
- **完整验证**: 对所有字段进行严格的数据验证
- **价格格式化**: 自动格式化价格为两位小数
- **库存管理**: 支持设置最低、最高和当前库存

### 2. 表单验证
- 物品名称：必填，最大100字符
- 物品编码：必填，最大50字符，自动生成或手动输入
- 物品类别：必填，从预设类别中选择
- 规格型号：可选，最大100字符
- 计量单位：必填，最大20字符，默认"个"
- 单价：必填，decimal(10,2)格式，最大99999999.99
- 库存数量：非负整数，最低库存不能大于最高库存
- 备注：可选，最大500字符

### 3. 用户体验优化
- **现代化UI**: 渐变色头部、圆角卡片、阴影效果
- **智能提示**: 清晰的占位符和错误提示
- **分组布局**: 基本信息和库存信息分组显示
- **响应式设计**: 适配不同屏幕尺寸
- **加载状态**: 提交时显示加载动画

## 📱 页面结构

### 库存管理主页 (`/pages/admin/inventory/index`)
- 显示物品列表
- 提供新增物品入口
- 支持搜索和筛选

### 新增物品页 (`/pages/admin/inventory/add`)
- 完整的物品信息录入表单
- 实时验证和错误提示
- 支持自动生成编码

## 🔧 技术实现

### 前端技术
- **框架**: uni-app + Vue 2.x
- **UI组件**: uView UI
- **样式**: SCSS + 现代化设计
- **表单验证**: 自定义验证规则

### 数据处理
- **类型转换**: 自动处理字符串到数字的转换
- **数据清理**: 自动去除首尾空格
- **格式化**: 价格自动格式化为两位小数

### 错误处理
- **API失败**: 提供模拟数据作为备选
- **网络异常**: 友好的错误提示
- **数据验证**: 详细的验证错误信息

## 🎨 设计规范

### 颜色方案
- **主色调**: 渐变蓝紫色 (#667eea → #764ba2)
- **成功色**: 渐变绿色 (#28a745 → #20c997)
- **背景色**: 渐变灰色 (#f5f7fa → #c3cfe2)
- **文字色**: #333333 (主要文字), #999999 (次要文字)

### 交互设计
- **点击反馈**: 缩放动画和颜色变化
- **焦点状态**: 边框高亮和阴影效果
- **加载状态**: 旋转动画和文字提示

## 📋 预设物品类别

1. **办公用品** (OFFICE): 文具、纸张、办公设备等
2. **教学用品** (TEACHING): 教具、图书、学习材料等
3. **清洁用品** (CLEANING): 清洁剂、工具、消毒用品等
4. **食品原料** (FOOD): 食材、调料、饮品等
5. **玩具用品** (TOY): 益智玩具、体感玩具等
6. **医疗用品** (MEDICAL): 药品、医疗器械、急救用品等
7. **体育用品** (SPORTS): 运动器材、户外用品等
8. **其他用品** (OTHER): 其他未分类物品

## 🚀 使用说明

1. **进入物品管理**: 从管理后台进入库存管理页面
2. **添加新物品**: 点击"添加物品"按钮
3. **填写信息**: 按照表单要求填写物品信息
4. **生成编码**: 点击"重新生成"按钮自动生成物品编码
5. **选择类别**: 从预设类别中选择合适的分类
6. **设置库存**: 填写当前库存和预警值
7. **保存物品**: 点击保存按钮完成添加

## 🔍 注意事项

- 物品编码在同一公司内必须唯一
- 单价支持最多8位整数和2位小数
- 库存数量必须为非负整数
- 最低库存不能大于最高库存
- 备注信息不能超过500字符

<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="header-content">
				<view class="nav-left" @click="goBack">
					<u-icon name="arrow-left" color="#ffffff" size="20"></u-icon>
				</view>
				<view class="header-title">
					<text class="title-text">新增班级</text>
				</view>
				<view class="nav-right" @click="saveClass">
					<u-icon name="checkmark" color="#ffffff" size="20"></u-icon>
				</view>
			</view>
		</view>

		<!-- 表单区域 -->
		<view class="form-container">
			<view class="form-section">
				<view class="section-title">基本信息</view>
				<u-form :model="classForm" ref="uForm" :rules="rules" label-width="160">
					<u-form-item label="班级名称" prop="name">
						<u-input 
							v-model="classForm.name" 
							placeholder="请输入班级名称"
							border
						/>
					</u-form-item>
					
					<u-form-item label="班级类型" prop="type">
						<u-input
							v-model="classForm.type"
							placeholder="请输入班级类型（如：小班、中班、大班等）"
							border
						/>
					</u-form-item>
					
					<u-form-item label="最大容量" prop="maxCapacity">
						<u-input 
							v-model="classForm.maxCapacity" 
							placeholder="请输入最大容量"
							type="number"
							border
						/>
					</u-form-item>
					
					<u-form-item label="教室位置" prop="location">
						<u-input 
							v-model="classForm.location" 
							placeholder="请输入教室位置"
							border
						/>
					</u-form-item>
				</u-form>
			</view>

			<view class="form-section">
				<view class="section-title">师资配置</view>
				<u-form :model="classForm" ref="uForm2" label-width="160">
					<u-form-item label="主班教师" prop="mainTeacher">
						<u-picker 
							:range="teacherOptions" 
							range-key="name"
							v-model="mainTeacherIndex"
							@change="onMainTeacherChange"
						>
							<u-input 
								:value="selectedMainTeacher" 
								placeholder="请选择主班教师"
								border
								readonly
							/>
						</u-picker>
					</u-form-item>
					
					<u-form-item label="副班教师" prop="assistantTeacher">
						<u-picker 
							:range="teacherOptions" 
							range-key="name"
							v-model="assistantTeacherIndex"
							@change="onAssistantTeacherChange"
						>
							<u-input 
								:value="selectedAssistantTeacher" 
								placeholder="请选择副班教师"
								border
								readonly
							/>
						</u-picker>
					</u-form-item>
				</u-form>
			</view>

			<view class="form-section">
				<view class="section-title">其他设置</view>
				<u-form :model="classForm" ref="uForm3" label-width="160">
					<u-form-item label="班级状态" prop="status">
						<u-radio-group v-model="classForm.status" placement="row">
							<u-radio label="active" name="active">正常</u-radio>
							<u-radio label="inactive" name="inactive">停用</u-radio>
						</u-radio-group>
					</u-form-item>
					
					<u-form-item label="班级描述" prop="description">
						<view class="textarea-wrapper">
							<textarea
								v-model="classForm.description"
								placeholder="请输入班级描述"
								maxlength="200"
								class="custom-textarea"
								auto-height
							/>
							<view class="char-count">{{ classForm.description.length }}/200</view>
						</view>
					</u-form-item>
				</u-form>
			</view>
		</view>

		<!-- 保存按钮 -->
		<view class="save-section">
			<view class="save-btn" @click="saveClass">
				<u-icon name="checkmark" color="#ffffff" size="20"></u-icon>
				<text class="save-text">保存班级</text>
			</view>
		</view>
	</view>
</template>

<script>
import {toast, useRouter} from '@/utils/utils.js'

export default {
	data() {
		return {
			classForm: {
				name: '',
				type: '',
				maxCapacity: '',
				location: '',
				mainTeacher: '',
				assistantTeacher: '',
				status: 'active',
				description: ''
			},
			mainTeacherIndex: 0,
			assistantTeacherIndex: 0,
			teacherOptions: [
				{ id: '', name: '请选择' },
				{ id: '1', name: '张老师' },
				{ id: '2', name: '李老师' },
				{ id: '3', name: '王老师' },
				{ id: '4', name: '刘老师' },
				{ id: '5', name: '陈老师' }
			],
			rules: {
				name: [
					{
						required: true,
						message: '请输入班级名称',
						trigger: ['change', 'blur']
					}
				],
				type: [
					{
						required: true,
						message: '请输入班级类型',
						trigger: ['change', 'blur']
					}
				],
				maxCapacity: [
					{
						required: true,
						message: '请输入最大容量',
						trigger: ['change', 'blur']
					},
					{
						pattern: /^[1-9]\d*$/,
						message: '请输入正确的数字',
						trigger: ['change', 'blur']
					}
				],
				location: [
					{
						required: true,
						message: '请输入教室位置',
						trigger: ['change', 'blur']
					}
				]
			}
		}
	},
	
	computed: {
		selectedMainTeacher() {
			if (this.classForm.mainTeacher) {
				const teacher = this.teacherOptions.find(t => t.id === this.classForm.mainTeacher)
				return teacher ? teacher.name : ''
			}
			return ''
		},
		
		selectedAssistantTeacher() {
			if (this.classForm.assistantTeacher) {
				const teacher = this.teacherOptions.find(t => t.id === this.classForm.assistantTeacher)
				return teacher ? teacher.name : ''
			}
			return ''
		}
	},
	
	methods: {
		goBack() {
			uni.navigateBack()
		},
		
		onMainTeacherChange(e) {
			const index = e.detail.value
			this.mainTeacherIndex = index
			this.classForm.mainTeacher = this.teacherOptions[index].id
		},
		
		onAssistantTeacherChange(e) {
			const index = e.detail.value
			this.assistantTeacherIndex = index
			this.classForm.assistantTeacher = this.teacherOptions[index].id
		},
		
		saveClass() {
			// 验证表单
			this.$refs.uForm.validate(valid => {
				if (valid) {
					// 模拟保存班级
					console.log('保存班级信息:', this.classForm)
					toast('班级创建成功')
					
					// 返回上一页
					setTimeout(() => {
						uni.navigateBack()
					}, 1500)
				} else {
					toast('请检查输入信息')
				}
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 顶部导航栏 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 0 30rpx;
	padding-top: var(--status-bar-height, 44rpx);
	box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}

.header-content {
	height: 120rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.nav-left, .nav-right {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.15);
	backdrop-filter: blur(10rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
	
	&:active {
		transform: scale(0.95);
		background: rgba(255, 255, 255, 0.25);
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
	}
}

.header-title {
	flex: 1;
	text-align: center;
}

.title-text {
	font-size: 36rpx;
	font-weight: 700;
	color: #ffffff;
}

/* 表单区域 */
.form-container {
	padding: 30rpx;
	padding-bottom: 200rpx;
}

.form-section {
	background: #ffffff;
	border-radius: 24rpx;
	padding: 40rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
}

.section-title {
	font-size: 32rpx;
	font-weight: 700;
	color: #333333;
	margin-bottom: 30rpx;
	padding-bottom: 20rpx;
	border-bottom: 2rpx solid #f0f0f0;
	position: relative;
	
	&::before {
		content: '';
		position: absolute;
		left: 0;
		bottom: -2rpx;
		width: 60rpx;
		height: 4rpx;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border-radius: 2rpx;
	}
}

/* 保存按钮 */
.save-section {
	position: fixed;
	bottom: 40rpx;
	left: 50%;
	transform: translateX(-50%);
	width: calc(100% - 60rpx);
	z-index: 100;
}

.save-btn {
	width: 100%;
	height: 100rpx;
	background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
	border-radius: 50rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 16rpx;
	box-shadow: 0 16rpx 40rpx rgba(40, 167, 69, 0.4);
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	
	&:active {
		transform: translateY(4rpx) scale(0.98);
		box-shadow: 0 8rpx 20rpx rgba(40, 167, 69, 0.6);
	}
}

.save-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #ffffff;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

/* uView组件样式覆盖 */
::v-deep .u-form-item {
	margin-bottom: 30rpx;
	
	.u-form-item__body {
		padding: 20rpx 0;
	}
	
	.u-form-item__body__left__content__label {
		font-size: 30rpx;
		color: #333333;
		font-weight: 500;
	}
}

::v-deep .u-input {
	font-size: 28rpx;
	color: #333333;
}

/* 自定义textarea样式 */
.textarea-wrapper {
	position: relative;
	border: 2rpx solid #e4e7ed;
	border-radius: 8rpx;
	padding: 20rpx;
	background: #ffffff;
}

.custom-textarea {
	width: 100%;
	min-height: 120rpx;
	font-size: 28rpx;
	color: #333333;
	line-height: 1.5;
	border: none;
	outline: none;
	resize: none;
	background: transparent;
}

.char-count {
	position: absolute;
	bottom: 10rpx;
	right: 15rpx;
	font-size: 22rpx;
	color: #999999;
}

::v-deep .u-radio-group {
	gap: 30rpx;
}

::v-deep .u-radio__text {
	font-size: 28rpx;
	color: #333333;
}
</style>

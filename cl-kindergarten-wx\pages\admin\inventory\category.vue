<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="header-content">
				<view class="nav-left" @click="goBack">
					<u-icon name="arrow-left" color="#ffffff" size="20"></u-icon>
				</view>
				<view class="header-title">
					<text class="title-text">类别管理</text>
				</view>
				<view class="nav-right"></view>
			</view>
		</view>

		<!-- 新增按钮 -->
		<view class="add-button-container">
			<view class="add-button" @click="showAddDialog">
				<u-icon name="plus" color="#ffffff" size="18"></u-icon>
				<text class="add-text">新增类别</text>
			</view>
		</view>

		<!-- 类别列表 -->
		<view class="category-list">
			<view v-for="category in categoryList" :key="category.categoryId" class="category-item">
				<view class="category-info">
					<view class="category-icon">📁</view>
					<view class="category-details">
						<text class="category-name">{{ category.categoryName }}</text>
						<text class="category-code">编码：{{ category.categoryCode }}</text>
					</view>
				</view>
				<view class="category-actions">
					<view class="action-btn edit" @click="editCategory(category)">
						<u-icon name="edit-pen" color="#667eea" size="16"></u-icon>
					</view>
					<view class="action-btn delete" @click="deleteCategory(category)">
						<u-icon name="trash" color="#ff4757" size="16"></u-icon>
					</view>
				</view>
			</view>
		</view>

		<!-- 空状态 -->
		<view v-if="categoryList.length === 0" class="empty-state">
			<view class="empty-icon">📂</view>
			<text class="empty-text">暂无类别数据</text>
			<view class="empty-btn" @click="showAddDialog">
				<text>添加第一个类别</text>
			</view>
		</view>

		<!-- 添加/编辑类别弹窗 -->
		<u-popup v-model="showDialog" mode="center" border-radius="24">
			<view class="dialog-content">
				<view class="dialog-header">
					<text class="dialog-title">{{ isEdit ? '编辑类别' : '新增类别' }}</text>
				</view>
				<view class="dialog-body">
					<view class="form-item">
						<text class="form-label">类别名称</text>
						<u-input 
							v-model="formData.categoryName" 
							placeholder="请输入类别名称"
							maxlength="50"
							:border="false"
							customStyle="background: #f8f9fa; border-radius: 12rpx; padding: 20rpx;"
						/>
					</view>
					<view class="form-item">
						<text class="form-label">类别编码</text>
						<u-input 
							v-model="formData.categoryCode" 
							placeholder="请输入类别编码"
							maxlength="20"
							:border="false"
							customStyle="background: #f8f9fa; border-radius: 12rpx; padding: 20rpx;"
						/>
					</view>
					<view class="form-item">
						<text class="form-label">备注</text>
						<u-input 
							v-model="formData.remark" 
							placeholder="请输入备注信息"
							maxlength="500"
							:border="false"
							customStyle="background: #f8f9fa; border-radius: 12rpx; padding: 20rpx;"
						/>
					</view>
				</view>
				<view class="dialog-footer">
					<view class="dialog-btn cancel" @click="hideDialog">
						<text>取消</text>
					</view>
					<view class="dialog-btn confirm" @click="handleSubmit">
						<text>{{ isEdit ? '更新' : '添加' }}</text>
					</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
import { toast } from '@/utils/utils.js'
import { getCategoryList, addCategory, updateCategory, deleteCategory } from '@/api/api.js'

export default {
	data() {
		return {
			categoryList: [],
			showDialog: false,
			isEdit: false,
			formData: {
				categoryId: '',
				categoryName: '',
				categoryCode: '',
				remark: ''
			},
			loading: false
		}
	},
	onLoad() {
		this.loadCategoryList()
	},
	methods: {
		goBack() {
			uni.navigateBack()
		},

		// 加载类别列表
		async loadCategoryList() {
			try {
				const res = await getCategoryList({ status: '0' })
				if (res.code === 200) {
					this.categoryList = res.rows || []
				}
			} catch (error) {
				console.error('加载类别列表失败:', error)
				// 使用模拟数据
				this.categoryList = [
					{ categoryId: 1, categoryName: '办公用品', categoryCode: 'OFFICE', remark: '办公室日常用品' },
					{ categoryId: 2, categoryName: '教学用品', categoryCode: 'TEACHING', remark: '教学相关用品' },
					{ categoryId: 3, categoryName: '清洁用品', categoryCode: 'CLEANING', remark: '清洁卫生用品' },
					{ categoryId: 4, categoryName: '食品原料', categoryCode: 'FOOD', remark: '食堂食品原料' },
					{ categoryId: 5, categoryName: '玩具用品', categoryCode: 'TOY', remark: '儿童玩具用品' },
					{ categoryId: 6, categoryName: '医疗用品', categoryCode: 'MEDICAL', remark: '医疗保健用品' },
					{ categoryId: 7, categoryName: '体育用品', categoryCode: 'SPORTS', remark: '体育运动用品' },
					{ categoryId: 8, categoryName: '其他用品', categoryCode: 'OTHER', remark: '其他未分类用品' }
				]
			}
		},

		// 显示添加对话框
		showAddDialog() {
			this.isEdit = false
			this.formData = {
				categoryId: '',
				categoryName: '',
				categoryCode: '',
				remark: ''
			}
			this.showDialog = true
		},

		// 编辑类别
		editCategory(category) {
			this.isEdit = true
			this.formData = {
				categoryId: category.categoryId,
				categoryName: category.categoryName,
				categoryCode: category.categoryCode,
				remark: category.remark || ''
			}
			this.showDialog = true
		},

		// 删除类别
		deleteCategory(category) {
			uni.showModal({
				title: '确认删除',
				content: `确定要删除类别"${category.categoryName}"吗？`,
				success: async (res) => {
					if (res.confirm) {
						try {
							const result = await deleteCategory(category.categoryId)
							if (result.code === 200) {
								toast('删除成功')
								this.loadCategoryList()
							} else {
								toast(result.msg || '删除失败')
							}
						} catch (error) {
							console.error('删除类别失败:', error)
							// 模拟删除成功
							this.categoryList = this.categoryList.filter(item => item.categoryId !== category.categoryId)
							toast('删除成功（模拟）')
						}
					}
				}
			})
		},

		// 隐藏对话框
		hideDialog() {
			this.showDialog = false
		},

		// 表单验证
		validateForm() {
			if (!this.formData.categoryName.trim()) {
				toast('请输入类别名称')
				return false
			}
			if (!this.formData.categoryCode.trim()) {
				toast('请输入类别编码')
				return false
			}
			if (this.formData.categoryName.length > 50) {
				toast('类别名称不能超过50个字符')
				return false
			}
			if (this.formData.categoryCode.length > 20) {
				toast('类别编码不能超过20个字符')
				return false
			}
			if (this.formData.remark && this.formData.remark.length > 500) {
				toast('备注不能超过500个字符')
				return false
			}
			return true
		},

		// 提交表单
		async handleSubmit() {
			if (!this.validateForm()) {
				return
			}

			if (this.loading) {
				return
			}

			this.loading = true
			try {
				const submitData = {
					categoryName: this.formData.categoryName.trim(),
					categoryCode: this.formData.categoryCode.trim().toUpperCase(),
					remark: this.formData.remark ? this.formData.remark.trim() : null,
					status: '0'
				}

				let res
				if (this.isEdit) {
					submitData.categoryId = this.formData.categoryId
					res = await updateCategory(submitData)
				} else {
					res = await addCategory(submitData)
				}

				if (res.code === 200) {
					toast(this.isEdit ? '更新成功' : '添加成功')
					this.hideDialog()
					this.loadCategoryList()
				} else {
					toast(res.msg || (this.isEdit ? '更新失败' : '添加失败'))
				}
			} catch (error) {
				console.error('提交失败:', error)
				// 模拟成功
				toast((this.isEdit ? '更新' : '添加') + '成功（模拟）')
				this.hideDialog()
				if (!this.isEdit) {
					// 模拟添加到列表
					const newCategory = {
						categoryId: Date.now(),
						categoryName: this.formData.categoryName.trim(),
						categoryCode: this.formData.categoryCode.trim().toUpperCase(),
						remark: this.formData.remark || ''
					}
					this.categoryList.push(newCategory)
				} else {
					// 模拟编辑，刷新列表
					this.loadCategoryList()
				}
			} finally {
				this.loading = false
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 顶部导航栏 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 0 30rpx;
	padding-top: var(--status-bar-height, 44rpx);
	box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}

.header-content {
	height: 120rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.nav-left {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.9);
		background: rgba(255, 255, 255, 0.3);
	}
}

.nav-right {
	width: 80rpx;
	height: 80rpx;
}

.header-title {
	flex: 1;
	text-align: center;
}

.title-text {
	font-size: 36rpx;
	font-weight: 600;
	color: #ffffff;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 新增按钮 */
.add-button-container {
	padding: 30rpx;
	padding-bottom: 0;
}

.add-button {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 24rpx;
	padding: 24rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 16rpx;
	box-shadow: 0 8rpx 30rpx rgba(102, 126, 234, 0.3);
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.98);
		box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.5);
	}
}

.add-text {
	font-size: 30rpx;
	font-weight: 600;
	color: #ffffff;
}

/* 类别列表 */
.category-list {
	padding: 30rpx;
	padding-top: 20rpx;
}

.category-item {
	background: #ffffff;
	border-radius: 24rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.98);
		box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.12);
	}
}

.category-info {
	display: flex;
	align-items: center;
	flex: 1;
}

.category-icon {
	font-size: 40rpx;
	margin-right: 24rpx;
}

.category-details {
	flex: 1;
}

.category-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	display: block;
	margin-bottom: 8rpx;
}

.category-code {
	font-size: 24rpx;
	color: #999999;
	display: block;
}

.category-actions {
	display: flex;
	gap: 16rpx;
}

.action-btn {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&.edit {
		background: rgba(102, 126, 234, 0.1);

		&:active {
			background: rgba(102, 126, 234, 0.2);
			transform: scale(0.9);
		}
	}

	&.delete {
		background: rgba(255, 71, 87, 0.1);

		&:active {
			background: rgba(255, 71, 87, 0.2);
			transform: scale(0.9);
		}
	}
}

/* 空状态 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 60rpx;
	text-align: center;
}

.empty-icon {
	font-size: 120rpx;
	margin-bottom: 40rpx;
	opacity: 0.6;
}

.empty-text {
	font-size: 28rpx;
	color: #999999;
	margin-bottom: 40rpx;
}

.empty-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #ffffff;
	padding: 20rpx 40rpx;
	border-radius: 50rpx;
	font-size: 28rpx;
	font-weight: 500;
	box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.95);
		box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.5);
	}
}

/* 对话框样式 */
.dialog-content {
	width: 600rpx;
	background: #ffffff;
	border-radius: 24rpx;
	overflow: hidden;
}

.dialog-header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 40rpx;
	text-align: center;
}

.dialog-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #ffffff;
}

.dialog-body {
	padding: 40rpx;
}

.form-item {
	margin-bottom: 30rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.form-label {
	font-size: 28rpx;
	font-weight: 500;
	color: #333333;
	margin-bottom: 16rpx;
	display: block;
}

.dialog-footer {
	display: flex;
	border-top: 1rpx solid #f0f0f0;
}

.dialog-btn {
	flex: 1;
	height: 100rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 30rpx;
	font-weight: 500;
	transition: all 0.3s ease;

	&.cancel {
		color: #999999;
		border-right: 1rpx solid #f0f0f0;

		&:active {
			background: #f8f9fa;
		}
	}

	&.confirm {
		color: #667eea;
		font-weight: 600;

		&:active {
			background: rgba(102, 126, 234, 0.1);
		}
	}
}
</style>

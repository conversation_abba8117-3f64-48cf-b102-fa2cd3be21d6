<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="header-content">
				<view class="nav-left" @click="goBack">
					<u-icon name="arrow-left" color="#ffffff" size="20"></u-icon>
				</view>
				<view class="header-title">
					<text class="title-text">库存管理</text>
				</view>
			</view>
		</view>

		<!-- 功能按钮区 -->
		<view class="function-section">
			<view class="function-row">
				<view class="function-btn primary" @click="addInventory">
					<view class="btn-icon">
						<u-icon name="plus" color="#ffffff" size="20"></u-icon>
					</view>
					<text class="btn-text">添加物品</text>
				</view>
				<view class="function-btn secondary" @click="manageCategory">
					<view class="btn-icon">
						<u-icon name="list" color="#667eea" size="20"></u-icon>
					</view>
					<text class="btn-text">类别管理</text>
				</view>
			</view>
		</view>

		<!-- 库存列表 -->
		<view class="inventory-list">
			<view v-for="item in filteredInventoryList" :key="item.id" class="inventory-item-card">
				<view class="item-header">
					<view class="item-icon">{{ item.icon }}</view>
					<view class="item-basic">
						<text class="item-name">{{ item.name }}</text>
						<text class="item-category">{{ item.category }}</text>
					</view>
					<view class="item-stock">
						<text class="stock-number" :class="{ 'low-stock': item.currentStock <= item.minStock }">
							{{ item.currentStock }}
						</text>
						<text class="stock-unit">{{ item.unit }}</text>
					</view>
				</view>
				
				<view class="item-details">
					<view class="detail-row">
						<text class="detail-label">规格:</text>
						<text class="detail-value">{{ item.specification }}</text>
					</view>
					<view class="detail-row">
						<text class="detail-label">单价:</text>
						<text class="detail-value">¥{{ item.price.toFixed(2) }}</text>
					</view>
					<view class="detail-row" v-if="item.currentStock <= item.minStock">
						<text class="detail-label warning">预警:</text>
						<text class="detail-value warning">库存不足(最低{{ item.minStock }}{{ item.unit }})</text>
					</view>
				</view>
				
				<view class="item-actions">
					<view class="action-row">
						<button class="action-btn primary" @click="stockIn(item)">
							<u-icon name="plus" size="14" color="#ffffff"></u-icon>
							<text>入库</text>
						</button>
						<button class="action-btn warning" @click="stockOut(item)">
							<u-icon name="minus" size="14" color="#ffffff"></u-icon>
							<text>出库</text>
						</button>
					</view>
					<view class="action-row">
						<button class="action-btn info" @click="editItem(item)">
							<u-icon name="edit-pen" size="14" color="#ffffff"></u-icon>
							<text>编辑</text>
						</button>
						<button class="action-btn danger" @click="deleteItem(item)">
							<u-icon name="trash" size="14" color="#ffffff"></u-icon>
							<text>删除</text>
						</button>
					</view>
				</view>
			</view>
			
			<!-- 加载更多提示 -->
			<view class="load-more" v-if="hasMore">
				<view class="load-more-loading" v-if="loadingMore">
					<u-loading-icon></u-loading-icon>
					<text class="load-text">正在加载...</text>
				</view>
				<view class="load-more-text" v-else>
					<text>上拉加载更多</text>
				</view>
			</view>
			
			<!-- 没有更多数据提示 -->
			<view class="no-more" v-if="!hasMore && inventoryList.length > 0">
				<text>没有更多数据了</text>
			</view>
			
			<!-- 空状态 -->
			<view class="empty-state" v-if="inventoryList.length === 0 && !loading">
				<view class="empty-icon">📦</view>
				<text class="empty-text">暂无物品数据</text>
				<button class="empty-btn" @click="addInventory">添加第一个物品</button>
			</view>
		</view>
	</view>
</template>

<script>
import { toast, useRouter } from '@/utils/utils.js'
import { getItemList, getCategoryList, deleteItem, updateItem, getItemDetail } from '@/api/api.js'

export default {
	data() {
		return {
			selectedCategory: 'all',
			categoryFilter: [
				{ label: '全部', value: 'all' }
			],
			inventoryList: [],
			loading: false,
			loadingMore: false,
			hasMore: true,
			pageNum: 1,
			pageSize: 10,
			total: 0
		}
	},
	onLoad() {
		this.loadCategoryList()
		this.loadInventoryList()
		
		// 监听刷新事件
		uni.$on('refreshInventoryList', () => {
			console.log('收到刷新列表事件')
			this.refreshList()
		})
	},
	
	onUnload() {
		// 页面卸载时移除事件监听
		uni.$off('refreshInventoryList')
	},
	
	// 触底加载更多
	onReachBottom() {
		if (!this.loadingMore && this.hasMore) {
			this.loadMoreData()
		}
	},
	computed: {
		filteredInventoryList() {
			if (this.selectedCategory === 'all') {
				return this.inventoryList
			}
			return this.inventoryList.filter(item => item.categoryId === this.selectedCategory)
		}
	},
	methods: {
		goBack() {
			uni.navigateBack()
		},
		
		addInventory() {
			uni.navigateTo({
				url: '/pages/admin/inventory/add'
			})
		},

		manageCategory() {
			uni.navigateTo({
				url: '/pages/admin/inventory/category'
			})
		},
		
		// 加载物品类别列表
		async loadCategoryList() {
			try {
				const res = await getCategoryList({ status: '0' })
				if (res.code === 200 && res.rows) {
					const categories = res.rows.map(item => ({
						label: item.categoryName,
						value: item.categoryId
					}))
					this.categoryFilter = [
						{ label: '全部', value: 'all' },
						...categories
					]
				}
			} catch (error) {
				console.error('加载类别列表失败:', error)
			}
		},

		// 加载物品列表
		async loadInventoryList(isRefresh = false) {
			if (this.loading) return
			
			this.loading = true
			try {
				const params = {
					status: '0',
					pageNum: this.pageNum,
					pageSize: this.pageSize
				}
				
				const res = await getItemList(params)
				if (res.code === 200) {
					const newItems = res.rows ? res.rows.map(item => ({
						id: item.itemId,
						name: item.itemName,
						category: item.categoryName || '未分类',
						categoryId: item.categoryId,
						specification: item.specification || '无',
						price: parseFloat(item.unitPrice || 0),
						unit: item.unit || '个',
						currentStock: parseInt(item.currentStock || 0),
						minStock: parseInt(item.minStock || 0),
						maxStock: parseInt(item.maxStock || 0),
						icon: this.getItemIcon(item.categoryName)
					})) : []
					
					if (isRefresh || this.pageNum === 1) {
						this.inventoryList = newItems
					} else {
						this.inventoryList = [...this.inventoryList, ...newItems]
					}
					
					this.total = res.total || 0
					this.hasMore = this.inventoryList.length < this.total
					
					console.log('成功加载物品列表:', newItems.length, '个物品，总计:', this.inventoryList.length)
				} else {
					throw new Error(res.msg || '获取物品列表失败')
				}
			} catch (error) {
				console.error('加载物品列表失败:', error)
				// 只有在列表为空时才使用模拟数据，避免覆盖已有数据
				if (this.inventoryList.length === 0) {
					this.inventoryList = [
						{
							id: 1,
							name: '大米',
							category: '食材',
							categoryId: 1,
							specification: '5kg装',
							price: 25.00,
							unit: '袋',
							currentStock: 8,
							minStock: 5,
							icon: '🌾'
						},
						{
							id: 2,
							name: '鸡蛋',
							category: '食材',
							categoryId: 1,
							specification: '新鲜鸡蛋',
							price: 0.80,
							unit: '个',
							currentStock: 120,
							minStock: 50,
							icon: '🥚'
						}
					]
					this.hasMore = false
					toast('网络异常，已加载示例数据')
				} else {
					toast('刷新失败，请稍后重试')
				}
			} finally {
				this.loading = false
			}
		},

		// 加载更多数据
		async loadMoreData() {
			if (this.loadingMore || !this.hasMore) return
			
			this.loadingMore = true
			this.pageNum += 1
			
			try {
				await this.loadInventoryList(false)
			} catch (error) {
				console.error('加载更多数据失败:', error)
				this.pageNum -= 1 // 回退页码
				toast('加载更多失败')
			} finally {
				this.loadingMore = false
			}
		},

		// 刷新列表
		async refreshList() {
			this.pageNum = 1
			this.hasMore = true
			await this.loadInventoryList(true)
		},

		// 根据类别名称获取图标
		getItemIcon(categoryName) {
			const iconMap = {
				'食材': '🌾',
				'食品原料': '🌾',
				'用品': '📦',
				'办公用品': '📝',
				'教学用品': '📚',
				'清洁用品': '🧼',
				'玩具': '🧸',
				'玩具用品': '🧸',
				'医疗用品': '🏥',
				'体育用品': '⚽',
				'其他用品': '📦'
			}
			return iconMap[categoryName] || '📦'
		},
		
		filterByCategory(category) {
			this.selectedCategory = category
		},
		
		// 入库操作
		stockIn(item) {
			uni.showModal({
				title: '物品入库',
				editable: true,
				placeholderText: '请输入入库数量',
				success: async (res) => {
					if (res.confirm && res.content) {
						const quantity = parseInt(res.content)
						if (isNaN(quantity) || quantity <= 0) {
							toast('请输入有效的数量')
							return
						}

						try {
							// 获取最新的物品信息
							const itemDetail = await getItemDetail(item.id)
							if (itemDetail.code === 200) {
								const currentStock = parseInt(itemDetail.data.currentStock || 0)
								const newStock = currentStock + quantity

								// 调用编辑接口更新库存
								const updateData = {
									...itemDetail.data,
									currentStock: newStock
								}

								const result = await updateItem(updateData)
								if (result.code === 200) {
									toast(`${item.name} 入库成功，当前库存：${newStock}${item.unit}`)
									this.refreshList()
								} else {
									toast(result.msg || '入库失败')
								}
							}
						} catch (error) {
							console.error('入库操作失败:', error)
							toast('入库操作失败，请稍后重试')
						}
					}
				}
			})
		},

		// 出库操作
		stockOut(item) {
			uni.showModal({
				title: '物品出库',
				editable: true,
				placeholderText: '请输入出库数量',
				success: async (res) => {
					if (res.confirm && res.content) {
						const quantity = parseInt(res.content)
						if (isNaN(quantity) || quantity <= 0) {
							toast('请输入有效的数量')
							return
						}

						if (quantity > item.currentStock) {
							toast('出库数量不能大于当前库存')
							return
						}

						try {
							// 获取最新的物品信息
							const itemDetail = await getItemDetail(item.id)
							if (itemDetail.code === 200) {
								const currentStock = parseInt(itemDetail.data.currentStock || 0)
								const newStock = Math.max(0, currentStock - quantity)

								// 调用编辑接口更新库存
								const updateData = {
									...itemDetail.data,
									currentStock: newStock
								}

								const result = await updateItem(updateData)
								if (result.code === 200) {
									toast(`${item.name} 出库成功，当前库存：${newStock}${item.unit}`)
									this.refreshList()
								} else {
									toast(result.msg || '出库失败')
								}
							}
						} catch (error) {
							console.error('出库操作失败:', error)
							toast('出库操作失败，请稍后重试')
						}
					}
				}
			})
		},
		
		editItem(item) {
			uni.navigateTo({
				url: `/pages/admin/inventory/edit?id=${item.id}`
			})
		},

		// 删除物品
		deleteItem(item) {
			uni.showModal({
				title: '确认删除',
				content: `确定要删除物品"${item.name}"吗？`,
				success: async (res) => {
					if (res.confirm) {
						try {
							const result = await deleteItem(item.id)
							if (result.code === 200) {
								toast('删除成功')
								// 删除成功后直接刷新列表
								this.refreshList()
							} else {
								toast(result.msg || '删除失败')
							}
						} catch (error) {
							console.error('删除物品失败:', error)
							// 模拟删除成功
							this.inventoryList = this.inventoryList.filter(i => i.id !== item.id)
							toast('删除成功（模拟）')
						}
					}
				}
			})
		},

		// 刷新列表
		onPullDownRefresh() {
			this.refreshList().finally(() => {
				uni.stopPullDownRefresh()
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 顶部导航栏 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 0 30rpx;
	padding-top: var(--status-bar-height, 44rpx);
	box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}

.header-content {
	height: 120rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.nav-left {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.9);
		background: rgba(255, 255, 255, 0.3);
	}
}

.header-title {
	flex: 1;
	text-align: center;
}

.title-text {
	font-size: 36rpx;
	font-weight: 600;
	color: #ffffff;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 新增物品按钮 */
.add-inventory-section {
	margin: 20rpx;
}

.add-inventory-btn {
	background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
	border-radius: 16rpx;
	padding: 20rpx 30rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 12rpx;
	box-shadow: 0 6rpx 20rpx rgba(40, 167, 69, 0.3);
	transition: all 0.3s ease;

	&:active {
		transform: translateY(-2rpx);
		box-shadow: 0 8rpx 25rpx rgba(40, 167, 69, 0.4);
	}
}

.add-btn-icon {
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
}

.icon-plus {
	font-size: 24rpx;
	font-weight: bold;
	color: #ffffff;
}

.add-btn-text {
	font-size: 28rpx;
	font-weight: 600;
	color: #ffffff;
}

/* 功能按钮区 */
.function-section {
	padding: 30rpx;
}

.function-row {
	display: flex;
	gap: 20rpx;
}

.function-btn {
	flex: 1;
	height: 100rpx;
	border-radius: 24rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 16rpx;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);

	&:active {
		transform: translateY(2rpx) scale(0.98);
	}

	&.primary {
		background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
		box-shadow: 0 16rpx 40rpx rgba(40, 167, 69, 0.4);

		&:active {
			box-shadow: 0 8rpx 20rpx rgba(40, 167, 69, 0.6);
		}

		.btn-text {
			color: #ffffff;
			font-weight: 600;
		}
	}

	&.secondary {
		background: #ffffff;
		border: 2rpx solid #667eea;
		box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.15);

		&:active {
			background: rgba(102, 126, 234, 0.05);
			box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.25);
		}

		.btn-text {
			color: #667eea;
			font-weight: 500;
		}
	}
}

.btn-icon {
	width: 40rpx;
	height: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.btn-text {
	font-size: 28rpx;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.category-filter {
	background: white;
	padding: 20rpx 40rpx;
	display: flex;
	gap: 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.filter-btn {
	background: #f5f5f5;
	border: none;
	border-radius: 40rpx;
	padding: 12rpx 24rpx;
	font-size: 24rpx;
	color: #666;
	
	&.active {
		background: #4CAF50;
		color: white;
	}
}

.inventory-list {
	padding: 16rpx 20rpx;
	padding-bottom: 40rpx;
}

.inventory-item-card {
	background: #ffffff;
	border-radius: 20rpx;
	margin-bottom: 16rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

	&:active {
		transform: scale(0.98);
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	}
}

.item-header {
	display: flex;
	align-items: center;
	padding: 24rpx 24rpx 16rpx 24rpx;
	gap: 16rpx;
}

.item-icon {
	width: 60rpx;
	height: 60rpx;
	background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
	flex-shrink: 0;
}

.item-basic {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 6rpx;
}

.item-name {
	font-size: 30rpx;
	font-weight: 600;
	color: #1a1a1a;
	line-height: 1.3;
}

.item-category {
	font-size: 22rpx;
	color: #8a8a8a;
	background: #f5f5f5;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	align-self: flex-start;
}

.item-stock {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	gap: 2rpx;
}

.stock-number {
	font-size: 32rpx;
	font-weight: 700;
	color: #28a745;
	line-height: 1;
	
	&.low-stock {
		color: #dc3545;
	}
}

.stock-unit {
	font-size: 20rpx;
	color: #8a8a8a;
}

.item-details {
	padding: 0 24rpx 16rpx 24rpx;
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.detail-row {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.detail-label {
	font-size: 22rpx;
	color: #666666;
	width: 60rpx;
	flex-shrink: 0;
	
	&.warning {
		color: #dc3545;
		font-weight: 500;
	}
}

.detail-value {
	font-size: 22rpx;
	color: #333333;
	
	&.warning {
		color: #dc3545;
		font-weight: 500;
	}
}

.item-actions {
	background: #f8f9fa;
	padding: 16rpx 24rpx;
	display: flex;
	flex-direction: column;
	gap: 12rpx;
}

.action-row {
	display: flex;
	gap: 12rpx;
}

.action-btn {
	flex: 1;
	height: 64rpx;
	border: none;
	border-radius: 12rpx;
	font-size: 24rpx;
	font-weight: 500;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 8rpx;
	transition: all 0.2s ease;
	
	&:active {
		transform: scale(0.95);
	}
	
	&.primary {
		background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
		color: #ffffff;
		box-shadow: 0 4rpx 12rpx rgba(40, 167, 69, 0.3);
		
		&:active {
			box-shadow: 0 2rpx 6rpx rgba(40, 167, 69, 0.4);
		}
	}
	
	&.warning {
		background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
		color: #ffffff;
		box-shadow: 0 4rpx 12rpx rgba(255, 193, 7, 0.3);
		
		&:active {
			box-shadow: 0 2rpx 6rpx rgba(255, 193, 7, 0.4);
		}
	}
	
	&.info {
		background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
		color: #ffffff;
		box-shadow: 0 4rpx 12rpx rgba(23, 162, 184, 0.3);
		
		&:active {
			box-shadow: 0 2rpx 6rpx rgba(23, 162, 184, 0.4);
		}
	}
	
	&.danger {
		background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
		color: #ffffff;
		box-shadow: 0 4rpx 12rpx rgba(220, 53, 69, 0.3);
		
		&:active {
			box-shadow: 0 2rpx 6rpx rgba(220, 53, 69, 0.4);
		}
	}
}

/* 加载更多相关样式 */
.load-more {
	padding: 30rpx 0;
	text-align: center;
}

.load-more-loading {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 16rpx;
}

.load-text {
	font-size: 24rpx;
	color: #999999;
}

.load-more-text {
	font-size: 24rpx;
	color: #cccccc;
}

.no-more {
	padding: 30rpx 0;
	text-align: center;
	
	text {
		font-size: 24rpx;
		color: #cccccc;
	}
}

/* 空状态样式 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 40rpx;
	text-align: center;
}

.empty-icon {
	font-size: 120rpx;
	margin-bottom: 24rpx;
	opacity: 0.6;
}

.empty-text {
	font-size: 28rpx;
	color: #999999;
	margin-bottom: 40rpx;
}

.empty-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #ffffff;
	border: none;
	border-radius: 40rpx;
	padding: 16rpx 40rpx;
	font-size: 26rpx;
	font-weight: 500;
	box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);
	
	&:active {
		transform: scale(0.95);
		box-shadow: 0 4rpx 10rpx rgba(102, 126, 234, 0.4);
	}
}
</style>

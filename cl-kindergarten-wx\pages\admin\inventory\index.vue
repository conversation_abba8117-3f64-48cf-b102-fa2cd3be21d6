<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="header-content">
				<view class="nav-left" @click="goBack">
					<u-icon name="arrow-left" color="#ffffff" size="20"></u-icon>
				</view>
				<view class="header-title">
					<text class="title-text">库存管理</text>
				</view>
			</view>
		</view>

		<!-- 分类筛选 -->
		<view class="category-filter">
			<button 
				v-for="(category, index) in categoryFilter" 
				:key="index"
				class="filter-btn"
				:class="{ active: selectedCategory === category.value }"
				@click="filterByCategory(category.value)"
			>
				{{ category.label }}
			</button>
		</view>

		<!-- 新增物品按钮 -->
		<view class="add-inventory-section">
			<view class="add-inventory-btn" @click="addInventory">
				<view class="add-btn-icon">
					<text class="icon-plus">+</text>
				</view>
				<text class="add-btn-text">添加物品</text>
			</view>
		</view>

		<!-- 库存列表 -->
		<view class="inventory-list">
			<view v-for="item in filteredInventoryList" :key="item.id" class="inventory-item-card">
				<view class="item-icon">{{ item.icon }}</view>
				<view class="item-details">
					<text class="item-name">{{ item.name }}</text>
					<text class="item-info">分类: {{ item.category }} | 规格: {{ item.specification }}</text>
					<text class="item-info">单价: ¥{{ item.price.toFixed(2) }} | 单位: {{ item.unit }}</text>
					<view class="stock-section">
						<text class="stock-label">库存:</text>
						<text class="stock-amount" :class="{ 'low-stock': item.currentStock <= item.minStock }">
							{{ item.currentStock }}{{ item.unit }}
						</text>
						<text class="stock-warning" v-if="item.currentStock <= item.minStock">
							(预警线: {{ item.minStock }}{{ item.unit }})
						</text>
					</view>
				</view>
				<view class="item-actions">
					<button class="action-btn in" @click="stockIn(item)">入库</button>
					<button class="action-btn out" @click="stockOut(item)">出库</button>
					<button class="action-btn edit" @click="editItem(item)">编辑</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { toast, useRouter } from '@/utils/utils.js'

export default {
	data() {
		return {
			selectedCategory: 'all',
			categoryFilter: [
				{ label: '全部', value: 'all' },
				{ label: '食材', value: '食材' },
				{ label: '用品', value: '用品' },
				{ label: '玩具', value: '玩具' }
			],
			inventoryList: [
				{
					id: 1,
					name: '大米',
					category: '食材',
					specification: '5kg装',
					price: 25.00,
					unit: '袋',
					currentStock: 8,
					minStock: 5,
					icon: '🌾'
				},
				{
					id: 2,
					name: '鸡蛋',
					category: '食材',
					specification: '新鲜鸡蛋',
					price: 0.80,
					unit: '个',
					currentStock: 120,
					minStock: 50,
					icon: '🥚'
				},
				{
					id: 3,
					name: '苹果',
					category: '食材',
					specification: '红富士',
					price: 8.00,
					unit: '斤',
					currentStock: 3,
					minStock: 10,
					icon: '🍎'
				},
				{
					id: 4,
					name: '湿纸巾',
					category: '用品',
					specification: '80抽装',
					price: 12.00,
					unit: '包',
					currentStock: 15,
					minStock: 10,
					icon: '🧻'
				},
				{
					id: 5,
					name: '积木',
					category: '玩具',
					specification: '大颗粒积木',
					price: 45.00,
					unit: '套',
					currentStock: 8,
					minStock: 5,
					icon: '🧱'
				},
				{
					id: 6,
					name: '彩色笔',
					category: '用品',
					specification: '24色装',
					price: 18.00,
					unit: '盒',
					currentStock: 2,
					minStock: 8,
					icon: '🖍️'
				}
			]
		}
	},
	computed: {
		filteredInventoryList() {
			if (this.selectedCategory === 'all') {
				return this.inventoryList
			}
			return this.inventoryList.filter(item => item.category === this.selectedCategory)
		}
	},
	methods: {
		goBack() {
			uni.navigateBack()
		},
		
		addInventory() {
			toast('添加物品功能开发中...')
			// useRouter('/pages/admin/inventory/add', {}, 'navigateTo')
		},
		
		filterByCategory(category) {
			this.selectedCategory = category
		},
		
		stockIn(item) {
			toast(`${item.name} 入库功能开发中...`)
			// 这里可以显示入库弹窗或跳转到入库页面
		},
		
		stockOut(item) {
			toast(`${item.name} 出库功能开发中...`)
			// 这里可以显示出库弹窗或跳转到出库页面
		},
		
		editItem(item) {
			toast(`编辑物品: ${item.name}`)
			// useRouter('/pages/admin/inventory/edit', { id: item.id }, 'navigateTo')
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 顶部导航栏 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 0 30rpx;
	padding-top: var(--status-bar-height, 44rpx);
	box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}

.header-content {
	height: 120rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.nav-left {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.9);
		background: rgba(255, 255, 255, 0.3);
	}
}

.header-title {
	flex: 1;
	text-align: center;
}

.title-text {
	font-size: 36rpx;
	font-weight: 600;
	color: #ffffff;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 新增物品按钮 */
.add-inventory-section {
	margin: 20rpx;
}

.add-inventory-btn {
	background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
	border-radius: 16rpx;
	padding: 20rpx 30rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 12rpx;
	box-shadow: 0 6rpx 20rpx rgba(40, 167, 69, 0.3);
	transition: all 0.3s ease;

	&:active {
		transform: translateY(-2rpx);
		box-shadow: 0 8rpx 25rpx rgba(40, 167, 69, 0.4);
	}
}

.add-btn-icon {
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
}

.icon-plus {
	font-size: 24rpx;
	font-weight: bold;
	color: #ffffff;
}

.add-btn-text {
	font-size: 28rpx;
	font-weight: 600;
	color: #ffffff;
}

.category-filter {
	background: white;
	padding: 20rpx 40rpx;
	display: flex;
	gap: 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.filter-btn {
	background: #f5f5f5;
	border: none;
	border-radius: 40rpx;
	padding: 12rpx 24rpx;
	font-size: 24rpx;
	color: #666;
	
	&.active {
		background: #4CAF50;
		color: white;
	}
}

.inventory-list {
	padding: 20rpx;
}

.inventory-item-card {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
	gap: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.08);
}

.item-icon {
	width: 80rpx;
	height: 80rpx;
	background: #e8f5e8;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 40rpx;
}

.item-details {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.item-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.item-info {
	font-size: 22rpx;
	color: #666;
	line-height: 1.4;
}

.stock-section {
	display: flex;
	align-items: center;
	gap: 12rpx;
	margin-top: 8rpx;
}

.stock-label {
	font-size: 24rpx;
	color: #666;
}

.stock-amount {
	font-size: 28rpx;
	font-weight: 600;
	color: #4CAF50;
	
	&.low-stock {
		color: #f44336;
	}
}

.stock-warning {
	font-size: 20rpx;
	color: #f44336;
}

.item-actions {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.action-btn {
	border: none;
	border-radius: 6rpx;
	padding: 8rpx 16rpx;
	font-size: 20rpx;
	
	&.in {
		background: #4CAF50;
		color: white;
	}
	
	&.out {
		background: #ff9800;
		color: white;
	}
	
	&.edit {
		background: #2196F3;
		color: white;
	}
}
</style>

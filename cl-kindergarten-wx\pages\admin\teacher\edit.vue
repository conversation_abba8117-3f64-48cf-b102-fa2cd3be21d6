<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="header-content">
				<view class="nav-left" @click="goBack">
					<u-icon name="arrow-left" color="#ffffff" size="20"></u-icon>
				</view>
				<view class="header-title">
					<text class="title-text">编辑教师</text>
				</view>
				<view class="nav-right" @click="saveTeacher">
					<u-icon name="checkmark" color="#ffffff" size="20"></u-icon>
				</view>
			</view>
		</view>

		<!-- 表单内容 -->
		<view class="form-container">
			<!-- 基本信息 -->
			<view class="form-section">
				<view class="section-title">
					<view class="title-icon">👤</view>
					<text>基本信息</text>
				</view>
				
				<!-- 姓名 -->
				<view class="form-item">
					<view class="form-label">
						<text class="required">*</text>
						<text>姓名</text>
					</view>
					<view class="input-wrapper">
						<u-input 
							v-model="formData.teacherName" 
							placeholder="请输入教师姓名"
							maxlength="50"
							:border="false"
							customStyle="background: #f8f9fa; border-radius: 12rpx; padding: 20rpx;"
						/>
					</view>
				</view>

				<!-- 工号 -->
				<view class="form-item">
					<view class="form-label">
						<text class="required">*</text>
						<text>工号</text>
					</view>
					<view class="input-wrapper">
						<u-input 
							v-model="formData.employeeNo" 
							placeholder="请输入工号"
							maxlength="20"
							:border="false"
							customStyle="background: #f8f9fa; border-radius: 12rpx; padding: 20rpx;"
						/>
					</view>
				</view>

				<!-- 性别 -->
				<view class="form-item">
					<view class="form-label">
						<text class="required">*</text>
						<text>性别</text>
					</view>
					<view class="input-wrapper" @click="showGenderPicker">
						<view class="picker-display">
							<text class="picker-text" :class="{ placeholder: !selectedGender }">
								{{ selectedGender ? selectedGender.label : '请选择性别' }}
							</text>
							<u-icon name="arrow-right" color="#c0c4cc" size="16"></u-icon>
						</view>
					</view>
				</view>

				<!-- 联系电话 -->
				<view class="form-item">
					<view class="form-label">
						<text class="required">*</text>
						<text>联系电话</text>
					</view>
					<view class="input-wrapper">
						<u-input 
							v-model="formData.phone" 
							placeholder="请输入联系电话"
							maxlength="11"
							type="number"
							:border="false"
							customStyle="background: #f8f9fa; border-radius: 12rpx; padding: 20rpx;"
						/>
					</view>
				</view>

				<!-- 身份证号 -->
				<view class="form-item">
					<view class="form-label">
						<text>身份证号</text>
					</view>
					<view class="input-wrapper">
						<u-input 
							v-model="formData.idCard" 
							placeholder="请输入身份证号"
							maxlength="18"
							:border="false"
							customStyle="background: #f8f9fa; border-radius: 12rpx; padding: 20rpx;"
						/>
					</view>
				</view>
			</view>

			<!-- 职位信息 -->
			<view class="form-section">
				<view class="section-title">
					<view class="title-icon">💼</view>
					<text>职位信息</text>
				</view>

				<!-- 职位 -->
				<view class="form-item">
					<view class="form-label">
						<text class="required">*</text>
						<text>职位</text>
					</view>
					<view class="input-wrapper" @click="showPositionPicker">
						<view class="picker-display">
							<text class="picker-text" :class="{ placeholder: !selectedPosition }">
								{{ selectedPosition ? selectedPosition.label : '请选择职位' }}
							</text>
							<u-icon name="arrow-right" color="#c0c4cc" size="16"></u-icon>
						</view>
					</view>
				</view>

				<!-- 入职时间 -->
				<view class="form-item">
					<view class="form-label">
						<text class="required">*</text>
						<text>入职时间</text>
					</view>
					<view class="input-wrapper" @click="showDatePicker">
						<view class="picker-display">
							<text class="picker-text" :class="{ placeholder: !formData.hireDate }">
								{{ formData.hireDate || '请选择入职时间' }}
							</text>
							<u-icon name="arrow-right" color="#c0c4cc" size="16"></u-icon>
						</view>
					</view>
				</view>

				<!-- 部门 -->
				<view class="form-item">
					<view class="form-label">
						<text>部门</text>
					</view>
					<view class="input-wrapper">
						<u-input 
							v-model="formData.department" 
							placeholder="请输入部门"
							maxlength="50"
							:border="false"
							customStyle="background: #f8f9fa; border-radius: 12rpx; padding: 20rpx;"
						/>
					</view>
				</view>

				<!-- 状态 -->
				<view class="form-item">
					<view class="form-label">
						<text>状态</text>
					</view>
					<view class="input-wrapper" @click="showStatusPicker">
						<view class="picker-display">
							<text class="picker-text" :class="{ placeholder: !selectedStatus }">
								{{ selectedStatus ? selectedStatus.label : '请选择状态' }}
							</text>
							<u-icon name="arrow-right" color="#c0c4cc" size="16"></u-icon>
						</view>
					</view>
				</view>
			</view>

			<!-- 薪资信息 -->
			<view class="form-section">
				<view class="section-title">
					<view class="title-icon">💰</view>
					<text>薪资信息</text>
				</view>

				<view class="salary-row">
					<!-- 基本工资 -->
					<view class="form-item half">
						<view class="form-label">
							<text>基本工资</text>
						</view>
						<view class="input-wrapper">
							<u-input 
								v-model="formData.baseSalary" 
								placeholder="0.00"
								type="digit"
								:border="false"
								customStyle="background: #f8f9fa; border-radius: 12rpx; padding: 20rpx;"
								@blur="formatSalary('baseSalary')"
							/>
						</view>
					</view>

					<!-- 课时费 -->
					<view class="form-item half">
						<view class="form-label">
							<text>课时费</text>
						</view>
						<view class="input-wrapper">
							<u-input 
								v-model="formData.hourlyRate" 
								placeholder="0.00"
								type="digit"
								:border="false"
								customStyle="background: #f8f9fa; border-radius: 12rpx; padding: 20rpx;"
								@blur="formatSalary('hourlyRate')"
							/>
						</view>
					</view>
				</view>
			</view>

			<!-- 备注信息 -->
			<view class="form-section">
				<view class="section-title">
					<view class="title-icon">📝</view>
					<text>备注信息</text>
				</view>

				<view class="form-item">
					<view class="form-label">
						<text>备注</text>
					</view>
					<view class="textarea-wrapper">
						<u-input 
							v-model="formData.remark" 
							placeholder="请输入备注信息"
							type="textarea"
							maxlength="500"
							:border="false"
							customStyle="background: #f8f9fa; border-radius: 12rpx; padding: 20rpx; min-height: 120rpx;"
						/>
						<view class="char-count">{{ formData.remark.length }}/500</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 保存按钮 -->
		<view class="save-section">
			<view class="save-btn" @click="saveTeacher" :class="{ loading: saving }">
				<view v-if="saving" class="loading-spinner"></view>
				<text>{{ saving ? '保存中...' : '保存修改' }}</text>
			</view>
		</view>

		<!-- 选择器弹窗 -->
		<!-- 性别选择器 -->
		<u-popup v-model="showGenderSelector" mode="bottom" border-radius="24">
			<view class="picker-popup">
				<view class="picker-header">
					<text class="picker-cancel" @click="showGenderSelector = false">取消</text>
					<text class="picker-title">选择性别</text>
					<text class="picker-confirm" @click="confirmGenderSelection">确定</text>
				</view>
				<picker-view class="picker-view" :value="genderPickerValue" @change="onGenderPickerChange">
					<picker-view-column>
						<view v-for="(item, index) in genderOptions" :key="index" class="picker-item">
							{{ item.label }}
						</view>
					</picker-view-column>
				</picker-view>
			</view>
		</u-popup>

		<!-- 职位选择器 -->
		<u-popup v-model="showPositionSelector" mode="bottom" border-radius="24">
			<view class="picker-popup">
				<view class="picker-header">
					<text class="picker-cancel" @click="showPositionSelector = false">取消</text>
					<text class="picker-title">选择职位</text>
					<text class="picker-confirm" @click="confirmPositionSelection">确定</text>
				</view>
				<picker-view class="picker-view" :value="positionPickerValue" @change="onPositionPickerChange">
					<picker-view-column>
						<view v-for="(item, index) in positionOptions" :key="index" class="picker-item">
							{{ item.label }}
						</view>
					</picker-view-column>
				</picker-view>
			</view>
		</u-popup>

		<!-- 状态选择器 -->
		<u-popup v-model="showStatusSelector" mode="bottom" border-radius="24">
			<view class="picker-popup">
				<view class="picker-header">
					<text class="picker-cancel" @click="showStatusSelector = false">取消</text>
					<text class="picker-title">选择状态</text>
					<text class="picker-confirm" @click="confirmStatusSelection">确定</text>
				</view>
				<picker-view class="picker-view" :value="statusPickerValue" @change="onStatusPickerChange">
					<picker-view-column>
						<view v-for="(item, index) in statusOptions" :key="index" class="picker-item">
							{{ item.label }}
						</view>
					</picker-view-column>
				</picker-view>
			</view>
		</u-popup>

		<!-- 日期选择器 -->
		<u-datetime-picker
			v-model="showDateSelector"
			mode="date"
			:max-date="maxDate"
			:min-date="minDate"
			@confirm="onDateConfirm"
			@cancel="showDateSelector = false"
		></u-datetime-picker>
	</view>
</template>

<script>
import { toast } from '@/utils/utils.js'
import { getTeacherDetail, updateTeacher } from '@/api/api.js'

export default {
	data() {
		return {
			teacherId: null,
			formData: {
				teacherName: '',
				employeeNo: '',
				gender: '',
				phone: '',
				idCard: '',
				position: '',
				hireDate: '',
				department: '',
				status: '',
				baseSalary: '',
				hourlyRate: '',
				remark: ''
			},
			saving: false,
			
			// 性别选择
			showGenderSelector: false,
			selectedGender: null,
			genderPickerValue: [0],
			tempGenderIndex: 0,
			genderOptions: [
				{ value: '男', label: '男' },
				{ value: '女', label: '女' }
			],
			
			// 职位选择
			showPositionSelector: false,
			selectedPosition: null,
			positionPickerValue: [0],
			tempPositionIndex: 0,
			positionOptions: [
				{ value: '园长', label: '园长' },
				{ value: '副园长', label: '副园长' },
				{ value: '主班教师', label: '主班教师' },
				{ value: '配班教师', label: '配班教师' },
				{ value: '保育员', label: '保育员' },
				{ value: '保健医', label: '保健医' },
				{ value: '厨师', label: '厨师' },
				{ value: '保安', label: '保安' },
				{ value: '财务', label: '财务' },
				{ value: '行政', label: '行政' }
			],

			// 状态选择
			showStatusSelector: false,
			selectedStatus: null,
			statusPickerValue: [0],
			tempStatusIndex: 0,
			statusOptions: [
				{ value: '0', label: '在职' },
				{ value: '1', label: '离职' },
				{ value: '2', label: '休假' }
			],
			
			// 日期选择
			showDateSelector: false,
			maxDate: new Date().getTime(),
			minDate: new Date('2000-01-01').getTime()
		}
	},

	async onLoad(options) {
		console.log('页面加载参数:', options)
		if (options.id) {
			this.teacherId = parseInt(options.id)
			await this.loadTeacherDetail()
		} else {
			console.error('缺少教师ID参数')
			toast('缺少教师ID参数')
			uni.navigateBack()
		}
	},

	methods: {
		goBack() {
			uni.navigateBack()
		},

		// 加载教师详情
		async loadTeacherDetail() {
			console.log('开始加载教师详情，ID:', this.teacherId)
			try {
				const res = await getTeacherDetail(this.teacherId)
				console.log('API响应:', res)
				if (res && res.code === 200 && res.data) {
					this.fillFormData(res.data)
				} else {
					console.log('API返回失败，使用模拟数据')
					this.loadMockTeacherData()
				}
			} catch (error) {
				console.error('API调用失败:', error)
				console.log('使用模拟数据进行测试')
				this.loadMockTeacherData()
			}
		},

		// 填充表单数据
		fillFormData(teacher) {
			console.log('填充表单数据:', teacher)
			this.formData = {
				teacherName: teacher.teacherName || '',
				employeeNo: teacher.employeeNo || '',
				gender: teacher.gender || '',
				phone: teacher.phone || '',
				idCard: teacher.idCard || '',
				position: teacher.position || '',
				hireDate: teacher.hireDate || '',
				department: teacher.department || '',
				status: teacher.status || '0',
				baseSalary: teacher.baseSalary ? teacher.baseSalary.toFixed(2) : '',
				hourlyRate: teacher.hourlyRate ? teacher.hourlyRate.toFixed(2) : '',
				remark: teacher.remark || ''
			}

			// 设置选中的选项
			if (teacher.gender) {
				this.selectedGender = this.genderOptions.find(item => item.value === teacher.gender)
			}
			if (teacher.position) {
				this.selectedPosition = this.positionOptions.find(item => item.value === teacher.position)
			}
			if (teacher.status !== undefined) {
				this.selectedStatus = this.statusOptions.find(item => item.value === teacher.status)
			}

			console.log('表单数据填充完成:', this.formData)
		},

		// 加载模拟教师数据（用于测试）
		loadMockTeacherData() {
			console.log('加载模拟教师数据')
			const mockTeacher = {
				teacherId: this.teacherId,
				teacherName: '张老师' + this.teacherId,
				employeeNo: `T20241231${String(this.teacherId).padStart(4, '0')}`,
				gender: '女',
				phone: '13800138000',
				idCard: '110101199001011234',
				position: '主班教师',
				hireDate: '2023-09-01',
				department: '大班组',
				status: '0',
				baseSalary: 5000.00,
				hourlyRate: 50.00,
				remark: '这是一个测试教师的备注信息'
			}
			this.fillFormData(mockTeacher)
		},

		// 显示性别选择器
		showGenderPicker() {
			if (this.selectedGender) {
				const index = this.genderOptions.findIndex(item => item.value === this.selectedGender.value)
				if (index >= 0) {
					this.genderPickerValue = [index]
					this.tempGenderIndex = index
				}
			}
			this.showGenderSelector = true
		},

		// 性别选择器变化
		onGenderPickerChange(e) {
			this.tempGenderIndex = e.detail.value[0]
		},

		// 确认性别选择
		confirmGenderSelection() {
			if (this.tempGenderIndex >= 0 && this.tempGenderIndex < this.genderOptions.length) {
				this.selectedGender = this.genderOptions[this.tempGenderIndex]
				this.formData.gender = this.selectedGender.value
			}
			this.showGenderSelector = false
		},

		// 显示职位选择器
		showPositionPicker() {
			if (this.selectedPosition) {
				const index = this.positionOptions.findIndex(item => item.value === this.selectedPosition.value)
				if (index >= 0) {
					this.positionPickerValue = [index]
					this.tempPositionIndex = index
				}
			}
			this.showPositionSelector = true
		},

		// 职位选择器变化
		onPositionPickerChange(e) {
			this.tempPositionIndex = e.detail.value[0]
		},

		// 确认职位选择
		confirmPositionSelection() {
			if (this.tempPositionIndex >= 0 && this.tempPositionIndex < this.positionOptions.length) {
				this.selectedPosition = this.positionOptions[this.tempPositionIndex]
				this.formData.position = this.selectedPosition.value
			}
			this.showPositionSelector = false
		},

		// 显示状态选择器
		showStatusPicker() {
			if (this.selectedStatus) {
				const index = this.statusOptions.findIndex(item => item.value === this.selectedStatus.value)
				if (index >= 0) {
					this.statusPickerValue = [index]
					this.tempStatusIndex = index
				}
			}
			this.showStatusSelector = true
		},

		// 状态选择器变化
		onStatusPickerChange(e) {
			this.tempStatusIndex = e.detail.value[0]
		},

		// 确认状态选择
		confirmStatusSelection() {
			if (this.tempStatusIndex >= 0 && this.tempStatusIndex < this.statusOptions.length) {
				this.selectedStatus = this.statusOptions[this.tempStatusIndex]
				this.formData.status = this.selectedStatus.value
			}
			this.showStatusSelector = false
		},

		// 显示日期选择器
		showDatePicker() {
			this.showDateSelector = true
		},

		// 日期确认
		onDateConfirm(e) {
			const date = new Date(e.value)
			this.formData.hireDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
			this.showDateSelector = false
		},

		// 格式化薪资
		formatSalary(field) {
			if (this.formData[field]) {
				const value = parseFloat(this.formData[field])
				if (!isNaN(value)) {
					this.formData[field] = value.toFixed(2)
				}
			}
		},

		// 表单验证
		validateForm() {
			if (!this.formData.teacherName.trim()) {
				toast('请输入教师姓名')
				return false
			}
			if (!this.formData.employeeNo.trim()) {
				toast('请输入工号')
				return false
			}
			if (!this.formData.gender) {
				toast('请选择性别')
				return false
			}
			if (!this.formData.phone.trim()) {
				toast('请输入联系电话')
				return false
			}
			if (!/^1[3-9]\d{9}$/.test(this.formData.phone)) {
				toast('请输入正确的手机号码')
				return false
			}
			if (!this.formData.position) {
				toast('请选择职位')
				return false
			}
			if (!this.formData.hireDate) {
				toast('请选择入职时间')
				return false
			}
			return true
		},

		// 保存教师信息
		async saveTeacher() {
			if (!this.validateForm()) {
				return
			}

			if (this.saving) {
				return
			}

			this.saving = true
			try {
				const submitData = {
					teacherId: this.teacherId,
					teacherName: this.formData.teacherName.trim(),
					employeeNo: this.formData.employeeNo.trim(),
					gender: this.formData.gender,
					phone: this.formData.phone.trim(),
					idCard: this.formData.idCard ? this.formData.idCard.trim() : null,
					position: this.formData.position,
					hireDate: this.formData.hireDate,
					department: this.formData.department ? this.formData.department.trim() : null,
					status: this.formData.status,
					baseSalary: this.formData.baseSalary ? parseFloat(this.formData.baseSalary) : 0,
					hourlyRate: this.formData.hourlyRate ? parseFloat(this.formData.hourlyRate) : 0,
					remark: this.formData.remark ? this.formData.remark.trim() : null
				}

				const res = await updateTeacher(submitData)
				if (res.code === 200) {
					toast('更新成功')
					uni.navigateBack()
				} else {
					toast(res.msg || '更新失败')
				}
			} catch (error) {
				console.error('更新教师失败:', error)
				// 模拟成功
				toast('更新成功（模拟）')
				setTimeout(() => {
					uni.navigateBack()
				}, 1000)
			} finally {
				this.saving = false
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
	padding-bottom: 120rpx;
}

/* 顶部导航栏 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 0 30rpx;
	padding-top: var(--status-bar-height, 44rpx);
	box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}

.header-content {
	height: 120rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.nav-left, .nav-right {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.9);
		background: rgba(255, 255, 255, 0.3);
	}
}

.header-title {
	flex: 1;
	text-align: center;
}

.title-text {
	font-size: 36rpx;
	font-weight: 600;
	color: #ffffff;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 表单容器 */
.form-container {
	padding: 30rpx;
}

.form-section {
	background: #ffffff;
	border-radius: 24rpx;
	padding: 40rpx 30rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
}

.section-title {
	display: flex;
	align-items: center;
	margin-bottom: 40rpx;
	padding-bottom: 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.title-icon {
	font-size: 32rpx;
	margin-right: 16rpx;
}

.section-title text {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

.form-item {
	margin-bottom: 30rpx;

	&:last-child {
		margin-bottom: 0;
	}

	&.half {
		width: calc(50% - 10rpx);
		display: inline-block;
		vertical-align: top;
	}
}

.salary-row {
	display: flex;
	justify-content: space-between;
}

.form-label {
	font-size: 28rpx;
	font-weight: 500;
	color: #333333;
	margin-bottom: 16rpx;
	display: flex;
	align-items: center;
}

.required {
	color: #ff4757;
	margin-right: 8rpx;
	font-size: 24rpx;
}

.input-wrapper {
	position: relative;
	display: flex;
	align-items: center;
}

.picker-display {
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 20rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 100%;
	min-height: 80rpx;
}

.picker-text {
	font-size: 28rpx;
	color: #333333;

	&.placeholder {
		color: #c0c4cc;
	}
}

.textarea-wrapper {
	position: relative;
}

.char-count {
	position: absolute;
	bottom: 16rpx;
	right: 20rpx;
	font-size: 24rpx;
	color: #999999;
}

/* 保存按钮 */
.save-section {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #ffffff;
	padding: 30rpx;
	box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.save-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 50rpx;
	height: 100rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 16rpx;
	box-shadow: 0 16rpx 40rpx rgba(102, 126, 234, 0.4);
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

	&:active {
		transform: translateY(4rpx) scale(0.98);
		box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.6);
	}

	&.loading {
		opacity: 0.8;
		pointer-events: none;
	}

	text {
		font-size: 32rpx;
		font-weight: 600;
		color: #ffffff;
		text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
	}
}

.loading-spinner {
	width: 32rpx;
	height: 32rpx;
	border: 3rpx solid rgba(255, 255, 255, 0.3);
	border-top: 3rpx solid #ffffff;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

/* 选择器弹窗 */
.picker-popup {
	background: #ffffff;
	border-radius: 24rpx 24rpx 0 0;
	overflow: hidden;
}

.picker-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.picker-cancel, .picker-confirm {
	font-size: 28rpx;
	color: #667eea;
	font-weight: 500;
}

.picker-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

.picker-view {
	height: 400rpx;
}

.picker-item {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 80rpx;
	font-size: 28rpx;
	color: #333333;
}
</style>

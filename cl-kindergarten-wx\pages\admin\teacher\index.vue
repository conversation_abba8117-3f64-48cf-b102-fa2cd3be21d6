<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="header-content">
				<view class="nav-left" @click="goBack">
					<u-icon name="arrow-left" color="#ffffff" size="20"></u-icon>
				</view>
				<view class="header-title">
					<text class="title-text">教师管理</text>
				</view>
			</view>
		</view>

		<!-- 功能按钮区 -->
		<view class="function-section">
			<!-- 搜索框 -->
			<view class="search-section">
				<view class="search-box">
					<u-icon name="search" color="#999" size="16"></u-icon>
					<u-input 
						v-model="searchKeyword" 
						placeholder="请输入教师姓名"
						:border="false"
						customStyle="flex: 1; margin-left: 12rpx;"
						@input="onSearchInput"
						@clear="clearSearch"
						clearable
					/>
				</view>
			</view>
			
			<view class="function-row">
				<view class="function-btn primary" @click="addTeacher">
					<view class="btn-icon">
						<u-icon name="plus" color="#ffffff" size="20"></u-icon>
					</view>
					<text class="btn-text">添加教师</text>
				</view>
				<view class="function-btn secondary" @click="syncTeacherData">
					<view class="btn-icon">
						<u-icon name="reload" color="#667eea" size="20"></u-icon>
					</view>
					<text class="btn-text">同步数据</text>
				</view>
			</view>
		</view>

		<!-- 教师列表 -->
		<view class="teacher-list">
			<!-- 加载状态 -->
			<view v-if="loading && teacherList.length === 0" class="loading-container">
				<view class="loading-spinner large"></view>
				<text class="loading-text">加载中...</text>
			</view>

			<!-- 教师列表项 -->
			<view v-for="teacher in teacherList" :key="teacher.teacherId" class="teacher-item-card">
				<view class="teacher-avatar">
					<image v-if="teacher.avatar" :src="teacher.avatar" class="avatar-img" mode="aspectFill"></image>
					<text v-else class="avatar-text">{{ formatGender(teacher.gender) === '男' ? '👨‍🏫' : '👩‍🏫' }}</text>
				</view>
				<view class="teacher-details">
					<view class="teacher-header">
						<text class="teacher-name">{{ teacher.teacherName }}</text>
						<view class="status-badge" :class="{ 'active': teacher.status === '0' }">
							{{ formatStatus(teacher.status) }}
						</view>
					</view>
					<text class="teacher-info">工号: {{ teacher.teacherCode || teacher.jobNumber || '-' }} | {{ teacher.position || '教师' }}</text>
					<text class="teacher-info">联系电话: {{ teacher.phone || '-' }}</text>
					<text v-if="teacher.hireDate" class="teacher-info">入职时间: {{ formatDate(teacher.hireDate) }}</text>
					<view class="salary-section" v-if="teacher.baseSalary">
						<text class="salary-label">基本工资:</text>
						<text class="salary-amount">¥{{ teacher.baseSalary ? teacher.baseSalary.toFixed(2) : '0.00' }}</text>
					</view>
				</view>
				<view class="teacher-actions">
					<button class="action-btn edit" @click="editTeacher(teacher)">编辑</button>
					<button class="action-btn delete" @click="deleteTeacher(teacher)">删除</button>
				</view>
			</view>

			<!-- 空状态 -->
			<view v-if="!loading && teacherList.length === 0" class="empty-container">
				<text class="empty-icon">👥</text>
				<text class="empty-text">暂无教师数据</text>
				<text class="empty-tip">点击上方"添加教师"按钮添加新教师</text>
			</view>

			<!-- 加载更多 -->
			<view v-if="loadingMore" class="loading-more">
				<view class="loading-spinner small"></view>
				<text class="loading-more-text">加载更多...</text>
			</view>

			<!-- 没有更多数据 -->
			<view v-if="!hasMore && teacherList.length > 0" class="no-more">
				<text class="no-more-text">没有更多数据了，共 {{ total }} 条</text>
			</view>
		</view>
	</view>
</template>

<script>
import { toast, useRouter } from '@/utils/utils.js'
import { getTeacherList, getTeacherDetail, deleteTeacher, syncTeacherData } from '@/api/api.js'

export default {
	data() {
		return {
			teacherList: [],
			loading: false,
			loadingMore: false,
			hasMore: true,
			pageNum: 1,
			pageSize: 10,
			total: 0,
			searchKeyword: '', // 搜索关键词
			searchTimer: null // 搜索防抖定时器
		}
	},

	onLoad() {
		this.loadTeacherList()
	},

	// 触底加载更多
	onReachBottom() {
		if (!this.loadingMore && this.hasMore) {
			this.loadMoreData()
		}
	},

	// 下拉刷新
	onPullDownRefresh() {
		this.refreshList().finally(() => {
			uni.stopPullDownRefresh()
		})
	},
	methods: {
		goBack() {
			uni.navigateBack()
		},

		// 添加教师
		addTeacher() {
			uni.navigateTo({
				url: '/pages/admin/teacher/add',
				events: {
					// 监听添加页面返回的刷新事件
					refreshTeacherList: () => {
						this.refreshList()
					}
				}
			})
		},

		// 同步教师数据
		syncTeacherData() {
			uni.showModal({
				title: '同步数据',
				content: '确定要从系统同步最新的教师数据吗？',
				success: (res) => {
					if (res.confirm) {
						this.performSync()
					}
				}
			})
		},

		// 执行同步操作
		async performSync() {
			uni.showLoading({
				title: '同步中...'
			})

			try {
				const res = await syncTeacherData()
				uni.hideLoading()

				if (res.code === 200) {
					toast('同步成功！已更新教师数据')
					// 重新加载教师列表
					this.refreshList()
				} else {
					toast(res.msg || '同步失败')
				}
			} catch (error) {
				uni.hideLoading()
				toast('同步失败，请稍后重试')
				console.error('同步教师数据失败:', error)
			}
		},

		// 加载教师列表
		async loadTeacherList() {
			if (this.loading) return

			this.loading = true
			try {
				const params = {
					pageNum: this.pageNum,
					pageSize: this.pageSize
				}
				
				// 添加搜索条件
				if (this.searchKeyword.trim()) {
					params.teacherName = this.searchKeyword.trim()
				}

				const res = await getTeacherList(params)

				if (res.code === 200) {
					const newList = res.rows || []

					if (this.pageNum === 1) {
						this.teacherList = newList
					} else {
						this.teacherList = [...this.teacherList, ...newList]
					}

					this.total = res.total || 0
					this.hasMore = this.teacherList.length < this.total
				} else {
					toast(res.msg || '加载失败')
				}
			} catch (error) {
				console.error('加载教师列表失败:', error)
				toast('加载失败，请稍后重试')
			} finally {
				this.loading = false
			}
		},

		// 加载更多数据
		async loadMoreData() {
			if (!this.hasMore || this.loadingMore) return

			this.loadingMore = true
			this.pageNum++

			try {
				await this.loadTeacherList()
			} catch (error) {
				this.pageNum-- // 失败时回退页码
			} finally {
				this.loadingMore = false
			}
		},

		// 刷新列表
		async refreshList() {
			this.pageNum = 1
			this.hasMore = true
			await this.loadTeacherList()
		},

		// 查看工资详情
		viewSalaryDetails(teacher) {
			toast(`查看 ${teacher.teacherName} 的工资详情`)
			// uni.navigateTo({
			// 	url: `/pages/admin/teacher/salary?id=${teacher.teacherId}`
			// })
		},

		// 编辑教师
		editTeacher(teacher) {
			uni.navigateTo({
				url: `/pages/admin/teacher/edit?id=${teacher.teacherId}`,
				events: {
					// 监听编辑页面返回的刷新事件
					refreshTeacherList: () => {
						this.refreshList()
					}
				}
			})
		},

		// 删除教师
		deleteTeacher(teacher) {
			uni.showModal({
				title: '确认删除',
				content: `确定要删除教师 ${teacher.teacherName} 吗？`,
				success: async (res) => {
					if (res.confirm) {
						try {
							uni.showLoading({ title: '删除中...' })
							const result = await deleteTeacher(teacher.teacherId)
							uni.hideLoading()

							if (result.code === 200) {
								toast('删除成功')
								this.refreshList()
							} else {
								toast(result.msg || '删除失败')
							}
						} catch (error) {
							uni.hideLoading()
							toast('删除失败，请稍后重试')
							console.error('删除教师失败:', error)
						}
					}
				}
			})
		},

		// 格式化日期
		formatDate(dateStr) {
			if (!dateStr) return ''
			return dateStr.split(' ')[0] // 只取日期部分
		},

		// 格式化性别
		formatGender(gender) {
			return gender === '0' ? '男' : '女'
		},

		// 格式化状态
		formatStatus(status) {
			return status === '0' ? '在职' : '离职'
		},

		// 搜索输入变化（防抖）
		onSearchInput() {
			if (this.searchTimer) {
				clearTimeout(this.searchTimer)
			}
			this.searchTimer = setTimeout(() => {
				this.searchTeacher()
			}, 500) // 500ms防抖
		},

		// 搜索教师
		searchTeacher() {
			this.pageNum = 1
			this.hasMore = true
			this.teacherList = []
			this.loadTeacherList()
		},

		// 清空搜索
		clearSearch() {
			this.searchKeyword = ''
			if (this.searchTimer) {
				clearTimeout(this.searchTimer)
			}
			this.pageNum = 1
			this.hasMore = true
			this.teacherList = []
			this.loadTeacherList()
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 顶部导航栏 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 0 30rpx;
	padding-top: var(--status-bar-height, 44rpx);
	box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}

.header-content {
	height: 120rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.nav-left {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.9);
		background: rgba(255, 255, 255, 0.3);
	}
}

.header-title {
	flex: 1;
	text-align: center;
}

.title-text {
	font-size: 36rpx;
	font-weight: 600;
	color: #ffffff;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 功能按钮区 */
.function-section {
	padding: 30rpx;
}

/* 搜索框 */
.search-section {
	margin-bottom: 20rpx;
}

.search-box {
	background: #ffffff;
	border-radius: 24rpx;
	padding: 20rpx 24rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.function-row {
	display: flex;
	gap: 20rpx;
}

.function-btn {
	flex: 1;
	height: 100rpx;
	border-radius: 24rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 16rpx;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);

	&:active {
		transform: translateY(2rpx) scale(0.98);
	}

	&.primary {
		background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
		box-shadow: 0 16rpx 40rpx rgba(40, 167, 69, 0.4);

		&:active {
			box-shadow: 0 8rpx 20rpx rgba(40, 167, 69, 0.6);
		}

		.btn-text {
			color: #ffffff;
			font-weight: 600;
		}
	}

	&.secondary {
		background: #ffffff;
		border: 2rpx solid #667eea;
		box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.15);

		&:active {
			background: rgba(102, 126, 234, 0.05);
			box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.25);
		}

		.btn-text {
			color: #667eea;
			font-weight: 500;
		}
	}
}

.btn-icon {
	width: 40rpx;
	height: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.btn-text {
	font-size: 28rpx;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.teacher-list {
	padding: 20rpx;
	padding-bottom: 40rpx;
}

/* 加载状态 */
.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 80rpx 0;
	gap: 20rpx;
}

.loading-text {
	font-size: 28rpx;
	color: #667eea;
}

.loading-spinner {
	border-radius: 50%;
	animation: spin 1s linear infinite;

	&.large {
		width: 60rpx;
		height: 60rpx;
		border: 4rpx solid #f3f3f3;
		border-top: 4rpx solid #667eea;
	}

	&.small {
		width: 32rpx;
		height: 32rpx;
		border: 3rpx solid #f3f3f3;
		border-top: 3rpx solid #667eea;
	}
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

/* 教师卡片 */
.teacher-item-card {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: flex-start;
	gap: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.08);
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.98);
		box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.12);
	}
}

.teacher-avatar {
	width: 80rpx;
	height: 80rpx;
	background: #fff3e0;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
	flex-shrink: 0;
	overflow: hidden;
}

.avatar-img {
	width: 100%;
	height: 100%;
	border-radius: 50%;
}

.avatar-text {
	font-size: 32rpx;
}

.teacher-details {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.teacher-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	gap: 12rpx;
}

.teacher-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	flex: 1;
}

.status-badge {
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	font-size: 20rpx;
	background: #f5f5f5;
	color: #999;

	&.active {
		background: #e8f5e8;
		color: #28a745;
	}
}

.teacher-info {
	font-size: 24rpx;
	color: #666;
	line-height: 1.4;
}

.salary-section {
	display: flex;
	align-items: center;
	gap: 12rpx;
	margin-top: 8rpx;
	flex-wrap: wrap;
}

.salary-label {
	font-size: 22rpx;
	color: #666;
}

.salary-amount {
	font-size: 24rpx;
	font-weight: 600;
	color: #4CAF50;
	margin-right: 20rpx;
}

.teacher-actions {
	display: flex;
	flex-direction: row;
	gap: 8rpx;
	flex-shrink: 0;
	align-items: flex-start;
}

.action-btn {
	border: none;
	border-radius: 8rpx;
	padding: 8rpx 12rpx;
	font-size: 20rpx;
	min-width: 50rpx;
	white-space: nowrap;

	&.edit {
		background: #2196F3;
		color: white;
	}

	&.delete {
		background: #f44336;
		color: white;
	}
}

/* 空状态 */
.empty-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 40rpx;
	gap: 20rpx;
}

.empty-icon {
	font-size: 80rpx;
	opacity: 0.5;
}

.empty-text {
	font-size: 32rpx;
	color: #999;
	font-weight: 500;
}

.empty-tip {
	font-size: 24rpx;
	color: #ccc;
	text-align: center;
	line-height: 1.5;
}

/* 加载更多 */
.loading-more {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 12rpx;
	padding: 30rpx 0;
}

.loading-more-text {
	font-size: 24rpx;
	color: #667eea;
}

/* 没有更多数据 */
.no-more {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 30rpx 0;
}

.no-more-text {
	font-size: 24rpx;
	color: #ccc;
}
</style>

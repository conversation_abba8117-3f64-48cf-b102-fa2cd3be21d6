export default {
    props: {
        // 是否打开组件
        show: {
            type: <PERSON><PERSON>an,
            default: () => {
                try {
                    return uni.$u?.props?.datetimePicker?.show ?? false
                } catch (e) {
                    return false
                }
            }
        },
        // 是否展示顶部的操作栏
        showToolbar: {
            type: <PERSON><PERSON><PERSON>,
            default: () => {
                try {
                    return uni.$u?.props?.datetimePicker?.showToolbar ?? true
                } catch (e) {
                    return true
                }
            }
        },
        // 绑定值
        value: {
            type: [String, Number],
            default: () => {
                try {
                    return uni.$u?.props?.datetimePicker?.value ?? ''
                } catch (e) {
                    return ''
                }
            }
        },
        // 顶部标题
        title: {
            type: String,
            default: () => {
                try {
                    return uni.$u?.props?.datetimePicker?.title ?? ''
                } catch (e) {
                    return ''
                }
            }
        },
        // 展示格式，mode=date为日期选择，mode=time为时间选择，mode=year-month为年月选择，mode=datetime为日期时间选择
        mode: {
            type: String,
            default: () => {
                try {
                    return uni.$u?.props?.datetimePicker?.mode ?? 'datetime'
                } catch (e) {
                    return 'datetime'
                }
            }
        },
        // 可选的最大时间
        maxDate: {
            type: Number,
            // 最大默认值为后10年
            default: () => {
                try {
                    return uni.$u?.props?.datetimePicker?.maxDate ?? new Date(new Date().getFullYear() + 10, 0, 1).getTime()
                } catch (e) {
                    return new Date(new Date().getFullYear() + 10, 0, 1).getTime()
                }
            }
        },
        // 可选的最小时间
        minDate: {
            type: Number,
            // 最小默认值为前10年
            default: () => {
                try {
                    return uni.$u?.props?.datetimePicker?.minDate ?? new Date(new Date().getFullYear() - 10, 0, 1).getTime()
                } catch (e) {
                    return new Date(new Date().getFullYear() - 10, 0, 1).getTime()
                }
            }
        },
        // 可选的最小小时，仅mode=time有效
        minHour: {
            type: Number,
            default: () => {
                try {
                    return uni.$u?.props?.datetimePicker?.minHour ?? 0
                } catch (e) {
                    return 0
                }
            }
        },
        // 可选的最大小时，仅mode=time有效
        maxHour: {
            type: Number,
            default: () => {
                try {
                    return uni.$u?.props?.datetimePicker?.maxHour ?? 23
                } catch (e) {
                    return 23
                }
            }
        },
        // 可选的最小分钟，仅mode=time有效
        minMinute: {
            type: Number,
            default: () => {
                try {
                    return uni.$u?.props?.datetimePicker?.minMinute ?? 0
                } catch (e) {
                    return 0
                }
            }
        },
        // 可选的最大分钟，仅mode=time有效
        maxMinute: {
            type: Number,
            default: () => {
                try {
                    return uni.$u?.props?.datetimePicker?.maxMinute ?? 59
                } catch (e) {
                    return 59
                }
            }
        },
        // 选项过滤函数
        filter: {
            type: [Function, null],
            default: () => {
                try {
                    return uni.$u?.props?.datetimePicker?.filter ?? null
                } catch (e) {
                    return null
                }
            }
        },
        // 选项格式化函数
        formatter: {
            type: [Function, null],
            default: () => {
                try {
                    return uni.$u?.props?.datetimePicker?.formatter ?? null
                } catch (e) {
                    return null
                }
            }
        },
        // 是否显示加载中状态
        loading: {
            type: Boolean,
            default: () => {
                try {
                    return uni.$u?.props?.datetimePicker?.loading ?? false
                } catch (e) {
                    return false
                }
            }
        },
        // 各列中，单个选项的高度
        itemHeight: {
            type: [String, Number],
            default: () => {
                try {
                    return uni.$u?.props?.datetimePicker?.itemHeight ?? 44
                } catch (e) {
                    return 44
                }
            }
        },
        // 取消按钮的文字
        cancelText: {
            type: String,
            default: () => {
                try {
                    return uni.$u?.props?.datetimePicker?.cancelText ?? '取消'
                } catch (e) {
                    return '取消'
                }
            }
        },
        // 确认按钮的文字
        confirmText: {
            type: String,
            default: () => {
                try {
                    return uni.$u?.props?.datetimePicker?.confirmText ?? '确认'
                } catch (e) {
                    return '确认'
                }
            }
        },
        // 取消按钮的颜色
        cancelColor: {
            type: String,
            default: () => {
                try {
                    return uni.$u?.props?.datetimePicker?.cancelColor ?? '#909193'
                } catch (e) {
                    return '#909193'
                }
            }
        },
        // 确认按钮的颜色
        confirmColor: {
            type: String,
            default: () => {
                try {
                    return uni.$u?.props?.datetimePicker?.confirmColor ?? '#3c9cff'
                } catch (e) {
                    return '#3c9cff'
                }
            }
        },
        // 每列中可见选项的数量
        visibleItemCount: {
            type: [String, Number],
            default: () => {
                try {
                    return uni.$u?.props?.datetimePicker?.visibleItemCount ?? 5
                } catch (e) {
                    return 5
                }
            }
        },
        // 是否允许点击遮罩关闭选择器
        closeOnClickOverlay: {
            type: Boolean,
            default: () => {
                try {
                    return uni.$u?.props?.datetimePicker?.closeOnClickOverlay ?? true
                } catch (e) {
                    return true
                }
            }
        },
        // 各列的默认索引
        defaultIndex: {
            type: Array,
            default: () => {
                try {
                    return uni.$u?.props?.datetimePicker?.defaultIndex ?? []
                } catch (e) {
                    return []
                }
            }
        }
    }
}
